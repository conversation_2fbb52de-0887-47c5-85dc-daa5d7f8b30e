[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/imgui-vm", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glad/v0.1.36/9e3539cb78a3425b96246554411a0b79/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/1a2eecb5aa6947bf9d3fa36ad611d9a6/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/1a2eecb5aa6947bf9d3fa36ad611d9a6/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/1a2eecb5aa6947bf9d3fa36ad611d9a6/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/1a2eecb5aa6947bf9d3fa36ad611d9a6/include/misc/cpp", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/main.cpp.o", "main.cpp"], "file": "main.cpp"}]