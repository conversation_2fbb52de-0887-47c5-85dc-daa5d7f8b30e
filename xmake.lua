add_rules("mode.debug", "mode.release")

-- Add required packages
add_requires("glfw", "glad", "opengl")
add_requires("imgui", {configs = {glfw = true, opengl3 = true}})

target("pianowo")
    set_kind("binary")
    set_languages("c++17")
    add_files("*.cpp")

    -- Add packages
    add_packages("glfw", "glad", "opengl", "imgui")

    -- Add system libraries for NixOS
    if is_plat("linux") then
        add_syslinks("GL", "X11", "Xrandr", "Xinerama", "Xcursor", "pthread", "dl", "asound")

        -- Note: BASS libraries are loaded dynamically at runtime
        -- No need to link them statically
    end
