// ==== このブロックをコピーして、main.cppの先頭に貼り付けてください ====

#include <iostream>
#include <glad/glad.h>
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

// エラーコールバック
static void glfw_error_callback(int error, const char* description) {
    fprintf(stderr, "Glfw Error %d: %s\n", error, description);
}

// フレームバッファオブジェクト（FBO）を管理する構造体
struct Framebuffer {
    GLuint ID = 0;
    GLuint TextureID = 0;
    int Width = 0;
    int Height = 0;
};

// FBOを作成するヘルパー関数
bool CreateFramebuffer(Framebuffer& fb, int width, int height) {
    fb.Width = width;
    fb.Height = height;

    glGenFramebuffers(1, &fb.ID);
    glBindFramebuffer(GL_FRAMEBUFFER, fb.ID);

    glGenTextures(1, &fb.TextureID);
    glBindTexture(GL_TEXTURE_2D, fb.TextureID);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, NULL);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, fb.TextureID, 0);

    if (glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE) {
        std::cerr << "ERROR::FRAMEBUFFER:: Framebuffer is not complete!" << std::endl;
        glBindFramebuffer(GL_FRAMEBUFFER, 0);
        return false;
    }

    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    return true;
}

// FBOを破棄するヘルパー関数
void DestroyFramebuffer(Framebuffer& fb) {
    glDeleteFramebuffers(1, &fb.ID);
    glDeleteTextures(1, &fb.TextureID);
    fb.ID = 0;
    fb.TextureID = 0;
}


int main() {
    // ------------------------------------------------------------------
    // 1. GLFW, GLAD, ウィンドウの初期化
    // ------------------------------------------------------------------
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) return 1;

    const char* glsl_version = "#version 330";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    GLFWwindow* window = glfwCreateWindow(1280, 720, "ImGui in ImGui (VM Example)", NULL, NULL);
    if (window == NULL) return 1;
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // VSync有効化

    if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
        std::cerr << "Failed to initialize GLAD" << std::endl;
        return -1;
    }

    // ------------------------------------------------------------------
    // 2. ImGuiコンテキストの作成 (メイン と セカンダリ)
    // ------------------------------------------------------------------
    IMGUI_CHECKVERSION();

    // --- メインコンテキスト ---
    ImGuiContext* main_context = ImGui::CreateContext();
    ImGui::SetCurrentContext(main_context);
    ImGuiIO& main_io = ImGui::GetIO();
    main_io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    ImGui::StyleColorsDark();
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // --- セカンダリ(VM)コンテキスト ---
    ImGuiContext* secondary_context = ImGui::CreateContext();
    ImGui::SetCurrentContext(secondary_context);
    ImGuiIO& secondary_io = ImGui::GetIO();
    secondary_io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    // セカンダリコンテキストは独立したスタイルを持つことができる
    ImGui::StyleColorsLight();

    // 作業コンテキストをメインに戻す
    ImGui::SetCurrentContext(main_context);


    // ------------------------------------------------------------------
    // 3. セカンダリコンテキスト描画用のFBOを作成
    // ------------------------------------------------------------------
    Framebuffer vm_framebuffer;
    if (!CreateFramebuffer(vm_framebuffer, 640, 480)) {
        return -1;
    }


    // ------------------------------------------------------------------
    // 4. メインループ
    // ------------------------------------------------------------------
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // === ステップA: セカンダリコンテキストをFBOにレンダリング ===
        {
            // FBOをバインド
            glBindFramebuffer(GL_FRAMEBUFFER, vm_framebuffer.ID);
            glViewport(0, 0, vm_framebuffer.Width, vm_framebuffer.Height);
            glClearColor(0.1f, 0.1f, 0.1f, 1.0f);
            glClear(GL_COLOR_BUFFER_BIT);

            // コンテキストをセカンダリに切り替え
            ImGui::SetCurrentContext(secondary_context);

            // IOを手動で設定（メインコンテキストから情報をコピー）
            secondary_io.DisplaySize = ImVec2((float)vm_framebuffer.Width, (float)vm_framebuffer.Height);
            secondary_io.DeltaTime = main_io.DeltaTime;
            // 本来は入力もここで処理するが、今回は表示のみのため省略

            // セカンダリのImGuiフレームを開始
            ImGui::NewFrame();

            // --- ここにVM内のUIを描画 ---
            ImGui::Begin("VM Window");
            ImGui::Text("This is the Virtual Machine's context!");
            ImGui::Text("It's rendered into a texture.");
            if (ImGui::Button("Button inside VM")) {
                std::cout << "VM Button was clicked!" << std::endl;
            }
            float color[3] = { 0.f, 1.f, 0.5f };
            ImGui::ColorEdit3("Color", color);
            ImGui::End();
            ImGui::ShowDemoWindow(); // デモウィンドウも表示できる

            // レンダリング
            ImGui::Render();
            ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

            // FBOのバインドを解除
            glBindFramebuffer(GL_FRAMEBUFFER, 0);
        }

        // === ステップB: メインコンテキストを画面にレンダリング ===
        {
            // コンテキストをメインに戻す
            ImGui::SetCurrentContext(main_context);

            // メインのImGuiフレームを開始
            ImGui_ImplOpenGL3_NewFrame();
            ImGui_ImplGlfw_NewFrame();
            ImGui::NewFrame();

            // --- ここにメインのUIを描画 ---

            ImGui::Begin("Main Controller");
            ImGui::Text("This is the Main Context.");
            ImGui::Separator();
            ImGui::Text("Below is the VM's output rendered as a texture:");

            // FBOのテクスチャをImGui::Imageで表示
            // OpenGLのテクスチャはY軸が反転しているため、UV座標を(0,1)-(1,0)に指定して正しく表示
            ImGui::Image(
                (void*)(intptr_t)vm_framebuffer.TextureID,
                ImVec2((float)vm_framebuffer.Width, (float)vm_framebuffer.Height),
                ImVec2(0, 1), // UV0 (Top-left)
                ImVec2(1, 0)  // UV1 (Bottom-right)
            );
            ImGui::End();

            // メインのデモウィンドウ
            ImGui::ShowDemoWindow();

            // 画面へのレンダリング
            int display_w, display_h;
            glfwGetFramebufferSize(window, &display_w, &display_h);
            glViewport(0, 0, display_w, display_h);
            glClearColor(0.45f, 0.55f, 0.60f, 1.00f);
            glClear(GL_COLOR_BUFFER_BIT);

            ImGui::Render();
            ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
        }

        glfwSwapBuffers(window);
    }

    // ------------------------------------------------------------------
    // 5. クリーンアップ
    // ------------------------------------------------------------------
    DestroyFramebuffer(vm_framebuffer);

    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();

    // 必ず両方のコンテキストを破棄する
    ImGui::DestroyContext(secondary_context);
    ImGui::DestroyContext(main_context);

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}