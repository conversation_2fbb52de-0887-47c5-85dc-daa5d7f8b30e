{
    fpc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    yasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    rust_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    gfortran_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    go_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    gcc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = {
            program = "/run/current-system/sw/bin/gcc",
            name = "gcc"
        },
        plat = "linux"
    },
    nasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    cross_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = false,
        plat = "linux"
    },
    envs_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    nim_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = false,
        plat = "linux"
    },
    cuda_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    swift_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    },
    fasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        __checked = true,
        plat = "linux"
    }
}